### Description

Describe the changes introduced by this PR and what does it affect

### Changes

List of changes this PR includes

### Related Issues

List of related issues

### Tested Scenarios

A list of scenarios tried to match the deliverables

### Documentation PR

For UI changes, Please provide the Documentation PR on [info_grid](https://github.com/threefoldtech/info_grid)

### To consider

#### Preliminary Checks:

- [ ] Preliminary Checks
  - Does it completely address the issue linked?
  - What about edge cases?
  - Does it meet the specified acceptance criteria?
  - Are there any unintended side effects?
  - Does the PR adhere to the team's coding conventions, style guides, and best practices?
  - Does it integrate well with existing features?
  - Does it impact the overall performance of the application?
  - Are there any bottlenecks or slowdowns?
  - Has it been optimized for efficiency?
  - Has it been adequately tested with unit, integration, and end-to-end tests?
  - Are there any known defects or issues?
  - Is the code well-documented?
  - Are changes to documentation reflected in the code?

#### UI Checks:

- [ ] UI Checks
  - If a UI design is provided/ does it follow it?
  - Does every button work?
  - Is the data displayed logical? Is it what you expected?
  - Does every validation work?
  - Does this interface feel intuitive?
  - What about slow network? Offline?
  - What if the gridproxy/graphql/chain is failing?
  - What would a first time user have a hard time navigating here?

#### Code Quality Checks:

- [ ] Code Quality Checks
  - Code formatted/linted? Are there unused imports? .. etc
  - Is there redundant/repeated code?
  - Are there conditionals that are always true or always false?
  - Can we write this more concisely?
  - Can we reuse this code? If yes, where?
  - Will the changes be easy to maintain and update in the future?
  - Can this code become too complex to understand for other devs?
  - Can this code cause future integration problems?

### Testing Checklist

- [ ] Does it Meet the specified acceptance criteria.
- [ ] Test if changes can affect any other functionality.
- [ ] Tested with unit, integration, and end-to-end tests.
- [ ] Tested the un-happy path and rollback scenarios.
- [ ] Documentation updated to meet the latest changes.
- [ ] Check automated tests working successfully with the changes.
- [ ] Can be covered by automated tests.

### General Checklist

- [ ] Tests included
- [ ] Build pass
- [ ] Documentation
- [ ] Code format and docstring
- [ ] Screenshots/Video attached (needed for UI changes)
