name: Grid Client Tests

on:
  schedule:
    - cron: 0 0 * * *
  workflow_dispatch:
    inputs:
      network:
        type: choice
        description: "Which network to run tests on"
        required: true
        default: "dev"
        options: 
          - "dev"
          - "qa"
          - "test"
          - "main"
jobs:
  tests:
    runs-on: ubuntu-latest
    env:
      NETWORK: ${{ github.event.inputs.network || 'dev' }}
      RMB_PROXY: true
      STORE_SECRET: secret
      MNEMONIC: ${{ secrets.MNEMONIC }}
    steps:
      - name: Set network for schedule
        if: ${{ !env.NETWORK }}
        run: echo "NETWORK=dev" >> $GITHUB_ENV
      - uses: actions/checkout@v4
      - name: Set up node version
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"
      - name: Install Yggdrasil and Add Peers
        run: |
          sudo apt-get update
          sudo apt-get install dirmngr
          gpg --fetch-keys https://neilalexander.s3.dualstack.eu-west-2.amazonaws.com/deb/key.txt
          gpg --export 569130E8CA20FBC4CB3FDE555898470A764B32C9 | sudo apt-key add -
          echo 'deb http://neilalexander.s3.dualstack.eu-west-2.amazonaws.com/deb/ debian yggdrasil' | sudo tee /etc/apt/sources.list.d/yggdrasil.list
          sudo apt-get update
          wget https://github.com/yggdrasil-network/yggdrasil-go/releases/download/v0.5.4/yggdrasil-0.5.4-amd64.deb
          sudo dpkg -i yggdrasil-0.5.4-amd64.deb
          sudo systemctl enable yggdrasil
          PEERS=$(curl https://raw.githubusercontent.com/AhmedHanafy725/yggdrasil-config/main/config.json | jq '.yggdrasil.peers[]' -r | sed 's/\//\\\//g' | sed -z 's/\n/\\n/g')
          sudo  sed -i -- 's/Peers\: \[\]/Peers: [\n'"$PEERS"']/g' /etc/yggdrasil/yggdrasil.conf
          sudo systemctl stop yggdrasil
          sudo systemctl start yggdrasil
      - name: Install Mycelium
        run: |
          sudo apt-get update
          wget https://github.com/threefoldtech/mycelium/releases/download/v0.5.6/mycelium-x86_64-unknown-linux-musl.tar.gz
          tar -xvf mycelium-x86_64-unknown-linux-musl.tar.gz
          mv mycelium /usr/local/bin
          sudo mycelium --peers tcp://188.40.132.242:9651 quic://185.69.166.8:9651 --tun-name utun9 &
      - name: Generate SSH Key
        run: |
          ssh-keygen -t ed25519 -N '' -f ~/.ssh/id_ed25519
      - name: Install deps
        run: |
          sudo apt-get update
          sudo apt-get install -y git libtool tmux redis net-tools
      - name: Install
        run: |
          yarn
          lerna run build --no-private
      - name: Run tests
        run: yarn workspace @threefold/grid_client test --runInBand --ci --coverage --colors --forceExit
        env:
          NODE_TLS_REJECT_UNAUTHORIZED: '0'  # Disable TLS/SSL certificate validation
      - name: Cleanup
        continue-on-error: true
        run: yarn run ts-node --project tsconfig-node.json packages/grid_client/tests/global_teardown.ts
      - name: Upload coverage to Codecov
        if: success()
        uses: codecov/codecov-action@v1
        with:
          flags: Grid Client
          token: ${{ secrets.CODECOV_TOKEN }}
      - name: Test Report
        uses: dorny/test-reporter@v1
        if: success() || failure()
        with:
          name: JEST Tests
          path: packages/grid_client/tests/test-reports/report.xml
          reporter: jest-junit
          fail-on-error: false
