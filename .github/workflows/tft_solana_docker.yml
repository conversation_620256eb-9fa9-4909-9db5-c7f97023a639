name: Stellar-Solana bridge Docker build/push
on:
  release:
    types: [published]

jobs:
  Build-and-push-docker-image:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@v2.1.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/threefoldtech/stellar_solana_bridge
          tags: |
            type=semver,pattern={{version}}
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          file: packages/stellar_solana_bridge/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VERSION=${{ steps.meta.outputs.version }}
