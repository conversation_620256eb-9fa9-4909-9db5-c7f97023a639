# This workflow will install needed dependencies and run main grid tests.

name: Grid Client Nightly

on:
  schedule:
    - cron: "0 4 * * *"
  workflow_dispatch:
permissions:
  contents: read
  issues: write

jobs:
  deployment-scripts:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        network: ["dev", "qa", "test", "main"]
    env:
      NETWORK: ${{ matrix.network }}
      RMB_PROXY: true
      STORE_SECRET: secret
      MNEMONIC: ${{ secrets.MNEMONIC }}
      SSH_KEY: ${{ secrets.SSH_KEY }}

    steps:
      - uses: actions/checkout@v4
        if: ${{ env.NETWORK == 'dev' }}
      - uses: actions/checkout@v4
        if: ${{ env.NETWORK == 'qa' }}
        with:
          ref: refs/tags/v2.8.0-rc4
      - uses: actions/checkout@v4
        if: ${{ env.NETWORK == 'test' }}
        with:
          ref: refs/tags/v2.8.0-rc4
      - uses: actions/checkout@v4
        if: ${{ env.NETWORK == 'main' }}
        with:
          ref: refs/tags/v2.6.3

      - name: Set up node 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: Install deps
        run: |
          sudo apt-get update
          sudo apt-get install -y git libtool tmux redis net-tools gh

      - name: Install
        run: |
          yarn
          make build

      - name: Run test dynamic single vm
        id: single_vm
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/dynamic_single_vm.ts

      - name: Run test Zos 3 lite gateway
        id: zos3lite_gateway_domain
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/zos3lite_gateway_domain.ts

      - name: Run test single ZOS3 lite vm with mycelium
        id: zos3lite
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/single_vm_zos3_lite.ts

      - name: Run test multiple vms
        id: multiple_vm
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/multiple_vms.ts

      - name: Run test multiple zos3 lite vms
        id: multiple_zos3_lite_vm
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/multiple_vms_zos_3_lite.ts

      - name: Run test kubernetes
        id: k8s
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/orchestrators/kubernetes_leader.ts

      - name: Run test vm with qsfs
        id: vmqsfs
        if: always()
        continue-on-error: true
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/vm_with_qsfs.ts

      - name: Run test kubernetes with qsfs
        id: k8sqsfs
        if: always()
        continue-on-error: true
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/orchestrators/kubernetes_with_qsfs.ts

      - name: Run test kvstore
        id: kvstore
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/kvstore_example.ts

      - name: Run test zdb
        id: zdb
        if: always()
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/zdb.ts

      - name: Cleanup - Delete all contracts
        if: always()
        id: delete_all
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/delete_all_contracts.ts

      - name: Run check up - List all contracts
        run: |
          sleep 15
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/list_all_contracts.ts > output.txt

      - name: Check if contracts are Empty
        run: |
          # print the file content
          cat output.txt

          # Check if Node contracts are empty
          if grep -q "nodeContracts: \[\]" output.txt && ! grep -q "nodeContracts: \[[[:space:]]*\]" output.txt; then
            echo "Deletion failed; The Node Contract is not empty."
            # Print the contract that couldn't be deleted
            exit 1
          fi

      - name: Find issues
        if: failure()
        id: find-issues
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          ISSUE_COUNT=$(gh issue list --search "${{github.workflow}} failed during schedule on ${{ env.NETWORK }}" --json title --jq '. | length')
          echo "issue_count=$ISSUE_COUNT" >> $GITHUB_ENV

      - name: Create GitHub Issue on Failure
        if: failure() && env.issue_count == '0'
        uses: dacbd/create-issue-action@main
        with:
          token: ${{ github.token }}
          title: ${{github.workflow}} failed during schedule on ${{ env.NETWORK }}
          body: |
            ## Failure Report

            > [!IMPORTANT]
            > **Details on failed run**: https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}

            - **Dynamic Single Vm**: ${{ steps.single_vm.outcome }}
            - **ZOS3 Lite Mycelium**: ${{ steps.zos3lite.outcome }}
            - **Multiple Vm**: ${{ steps.multiple_vm.outcome }}
            - **Multiple Vm zos3 lite**: ${{ steps.multiple_zos3_lite_vm.outcome }}
            - **zos3 lite gateway**: ${{ steps.zos3lite_gateway_domain.outcome }}
            - **Kubernetes**: ${{ steps.k8s.outcome }}
            - **Vmq QSFS**: skipped https://github.com/threefoldtech/tfgrid-sdk-ts/issues/3611
            - **Kubernetes QSFS**: skipped https://github.com/threefoldtech/tfgrid-sdk-ts/issues/3611
            - **Kvstore**: ${{ steps.kvstore.outcome }}
            - **Zdb**: ${{ steps.zdb.outcome }}
            - **Delete all contracts**: ${{ steps.delete_all.outcome }}
          labels: type_bug, grid_client
