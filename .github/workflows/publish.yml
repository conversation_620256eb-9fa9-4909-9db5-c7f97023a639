# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Full Clients Publish

on:
  push:
    tags:
      - "**"

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/
    env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: "https://registry.npmjs.org"
          cache: "yarn"

      - name: Install
        run: yarn --pure-lockfile
      - name: Build
        run: lerna run build --no-private

      - name: Publish Pre-release
        if: contains(github.ref, 'rc') ||  contains(github.ref, 'alpha')
        run: lerna publish from-package --no-private --yes --dist-tag edge

      - name: Publish Release
        if: ${{ !contains(github.ref, 'rc') && !contains(github.ref, 'alpha') }}
        run: lerna publish from-package --no-private --yes
        

  docker-image-grid-client:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@v2.1.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/threefoldtech/grid_client
          tags: |
            type=semver,pattern={{version}}
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: packages/grid_client
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            GRID_VERSION=${{ steps.meta.outputs.version }}

  docker-image-grid-http-server:
    needs: build
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - name: Checkout the repo
        uses: actions/checkout@v4
      - name: Log in to the Container registry
        uses: docker/login-action@v2.1.0
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Extract metadata for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ghcr.io/threefoldtech/grid_http_server
          tags: |
            type=semver,pattern={{version}}
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: packages/grid_http_server
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            GRID_VERSION=${{ steps.meta.outputs.version }}
