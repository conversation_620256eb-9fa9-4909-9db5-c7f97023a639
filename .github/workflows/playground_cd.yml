# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and deploy to staging server by Copying the artifacts using ssh to play.dev.grid.tf

name: Playground CD

on:
  push:
    branches:
      - development
      - development_2.7
    paths:
      - "packages/playground/**"
  workflow_dispatch:

concurrency:
  # one instance of runing pipeline allowed per branch.
  # On master, we want all builds to complete even if merging happens faster for better reliability and to make it easier to discover at which point something broke.
  # on developmetn, new workflow trigger will cancel the in progress job, and start a new one..
  group: ${{ format('ci-{0}', github.ref) }}
  cancel-in-progress: ${{ github.event_name == 'release' && github.event.release.prerelease == false }}
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: "--max-old-space-size=8192"

    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install dependencies
        run: |
          yarn install

      - name: Build in case development
        run: |
          lerna run build --no-private
          cd packages/playground
          VERSION=$GITHUB_SHA
          VERSION=${VERSION:0:7} NETWORK=dev yarn build

      - name: Copying files to staging server
        if: ${{ github.ref == 'refs/heads/development' }}
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.DEV_REMOTE_HOST }}
          username: ${{ secrets.DEV_REMOTE_USER }}
          key: ${{ secrets.DEV_SSH_KEY }}
          passphrase: ${{ secrets.DEV_SSH_KEY_PASSPHRASE }}
          rm: true
          port: ${{ secrets.PORT }}
          source: "packages/playground/dist/"
          target: "${{ secrets.PLAYGROUND_REMOTE_DIR }}"
          strip_components: 3

      - name: Copying files to staging server
        if: ${{ github.ref == 'refs/heads/development_313' }}
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.DEV_REMOTE_HOST }}
          username: ${{ secrets.DEV_REMOTE_USER }}
          key: ${{ secrets.DEV_SSH_KEY }}
          passphrase: ${{ secrets.DEV_SSH_KEY_PASSPHRASE }}
          rm: true
          port: ${{ secrets.PORT }}
          source: "packages/playground/dist/"
          target: "/opt/playground_new/devnet/dist"
          strip_components: 3
