# This workflow will do a clean install of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Full Clients Build

on:
  push:
    branches: 
      - development
      - development_2.7

  pull_request:
    branches: 
      - development
      - development_2.7

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install
        run: yarn
      - name: Build
        run: lerna run build --no-private
