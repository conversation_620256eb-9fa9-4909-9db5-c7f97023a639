name: Mass Deployments

on:
  schedule:
    - cron: "0 */6 * * *"
  workflow_dispatch:

jobs:
  mass-deployments:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
    env:
      NETWORK: main
      RMB_PROXY: true
      STORE_SECRET: secret
      MNEMONIC: ${{ secrets.MNEMONIC }}
      SSH_KEY: ${{ secrets.SSH_KEY }}

    steps:
      - uses: actions/checkout@v4
        with:
          ref: refs/tags/v2.7.0-rc1
      - name: Set up node 18
        uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: "yarn"

      - name: Install deps
        run: |
          sudo apt-get update
          sudo apt-get install -y git libtool tmux redis net-tools
      - name: Install
        run: |
          yarn
          lerna run build --no-private

      - name: Run test mass deployments
        id: massdeployments
        continue-on-error: true
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/mass_deployments.ts

      - name: Cleanup - Delete all contracts
        id: deleteall
        run: |
          yarn run ts-node --project packages/grid_client/tsconfig-node.json packages/grid_client/scripts/delete_all_contracts.ts

      - name: Test Results
        run: |
          echo Batch Vms: ${{ steps.massdeployments.outcome }}
          echo Delete all contracts: ${{ steps.deleteall.outcome }}

      - name: Check test script status
        if: |
          steps.massdeployments.outcome != 'success'
        run: exit 1

      - name: Send Telegram Notification
        env:
          TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN }}
          TELEGRAM_CHAT_ID: ${{ secrets.TELEGRAM_CHAT_ID }}
        run: |
          MESSAGE="Deployment Summary:\\nSuccessful Deployments: ${{ steps.massdeployments.outputs.success_count }}\\nFailed Deployments: ${{ steps.massdeployments.outputs.failed_count }}\\nErrors: ${{ steps.massdeployments.outputs.errors }}"
          curl -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage" -d "chat_id=${TELEGRAM_CHAT_ID}&text=${MESSAGE}&parse_mode=Markdown"
