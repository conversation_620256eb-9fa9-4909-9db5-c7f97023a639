# This workflow will do a clean install of node dependencies, cache/restore them, build the source code using yarn build:app

name: Playground Build

on:
  push:
    branches: 
      - development
      - development_2.7
    paths:
      - "packages/playground/**"
  pull_request:
    branches: 
      - development
      - development_2.7
    paths:
      - "packages/playground/**"

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: "--max-old-space-size=8192"

    strategy:
      matrix:
        node-version: [18.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "yarn"
          cache-dependency-path: "**/yarn.lock"

      - name: Install dependencies
        run: |
          yarn install


      - name: Build
        run: |
          lerna run build --no-private
          cd packages/playground
          yarn build
