name: Bug Report
description: Create a bug report
title: "🐞 [Bug]: "
labels: ["type_bug"]
body:
  - type: markdown
    attributes:
      value: |
        #### Please fill the fields to help us solving your bug! Don't forget to add the right label.
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered.
      options:
      - label: I have searched the existing issues
        required: true

  - type: dropdown
    id: package
    attributes:
      label: which package/s did you face the problem with?
      multiple: true
      options:
        - Dashboard
        - Stats
        - graphql_client
        - grid_client
        - grid_http_server
        - gridproxy_client
        - grid_rmb_server
        - Monitoring
        - rmb_direct_client
        - rmb_peer_client
        - rmb_peer_server
        - tfchain_client
        - types
        - UI
    validations:
      required: true

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Describe your bug!
    validations:
      required: true

  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. In this environment...
        1. With this config...
        1. Run '...'
        1. See error...
    validations:
      required: false
      
  - type: dropdown
    id: networks
    attributes:
      label: which network/s did you face the problem on?
      multiple: true
      options:
        - Dev
        - QA
        - Test
        - Main
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: version
    validations:
      required: true

  - type: input
    id: twin_id
    attributes:
      label: Twin ID/s
    validations:
      required: false

  - type: input
    id: node_id
    attributes:
      label: Node ID/s
    validations:
      required: false

  - type: input
    id: farm_id
    attributes:
      label: Farm ID/s
    validations:
      required: false

  - type: input
    id: contract_id
    attributes:
      label: Contract ID/s
    validations:
      required: false

  - type: textarea
    id: screenshots
    attributes:
      label: Relevant screenshots/screen records
      description: |
       Please provide any relevant screenshots/screen records, this is very important for UI issues.
       Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks. 
      render: console
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ### Thanks for taking the time to fill out this bug report!