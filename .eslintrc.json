{
  "root": true,
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:cypress/recommended",
    "prettier",
    "plugin:vue/essential",
    "eslint:recommended",
    "@vue/typescript/recommended"
  ],
  "parserOptions": {
    "parser": "@typescript-eslint/parser",
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "plugins": ["@typescript-eslint", "prettier", "simple-import-sort", "svelte3"],
  "overrides": [
    {
      "files": ["*.svelte", "**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"],
      "processor": "svelte3/svelte3",
      "env": {
        "jest": true
      }
    }
  ],
  "rules": {
    "no-console": "off",
    "no-async-promise-executor": "off",
    "prettier/prettier": "warn",
    "@typescript-eslint/no-var-requires": "off",
    "simple-import-sort/imports": "error",
    "prefer-spread": "off",
    "@typescript-eslint/no-explicit-any": "off",
    "vue/multi-word-component-names": "off",
    "vue/no-v-text-v-html-on-component": "off",
    "@typescript-eslint/no-empty-function": "off",
    "vue/no-v-for-template-key": "off",
    "vue/no-multiple-template-root": "off",
    "vue/no-v-model-argument": "off",
    "@typescript-eslint/ban-types": [
      "error",
      {
        "extendDefaults": true,
        "types": {
          "{}": false
        }
      }
    ]
  },
  "settings": {
    "svelte3/typescript": true // load TypeScript as peer dependency
  },
  "ignorePatterns": [
    "**/node_modules/**",
    "**/dist/**",
    "**/docs/**",
    "/packages/rmb_direct_client/lib/types/lib/**",
    "packages/stats/public/build/*",
    "packages/UI/*.config.js",
    "packages/UI/src/index.css",
    "*.config.*",
    "*global.css"
  ]
}
