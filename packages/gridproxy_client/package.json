{"name": "@threefold/gridproxy_client", "version": "2.8.0-rc4", "description": "gridproxy_client help to interact with gridproxy based on network", "main": "./dist/node/public_api.js", "module": "./dist/es6/public_api.js", "exports": {"require": "./dist/node/public_api.js", "import": "./dist/es6/public_api.js"}, "types": "dist/es6/public_api.d.ts", "files": ["/dist"], "repository": "**************:MohamedElmdary/gridproxy_client.git", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "private": false, "publishConfig": {"access": "public"}, "scripts": {"build": "npm run es6-build && npm run node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig.json"}, "devDependencies": {"typescript": "^4.8.4"}}