{"name": "@threefold/stellar_solana_bridge", "version": "2.8.0-rc4", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "@solana/web3.js": "^1.98.0", "@stellar/stellar-sdk": "^13.1.0", "@vitejs/plugin-vue": "^5.2.3", "bs58": "^6.0.0", "qrcode": "^1.5.4", "vue": "^3.5.13", "vuetify": "^3.7.18"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.3", "@types/node": "^18.17.17", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.7.0", "jsdom": "^22.1.0", "npm-run-all2": "^6.0.6", "sass": "^1.86.0", "start-server-and-test": "^2.0.0", "typescript": "~5.2.0", "vite": "^4.4.9", "vite-plugin-vuetify": "^2.1.0", "vue-tsc": "^1.8.11", "vuetify": "^3.7.18"}}