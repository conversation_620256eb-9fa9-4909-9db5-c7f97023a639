# Stellar-Solana Bridge

This project provides a user-friendly interface for transferring tokens between the Stellar network and the Solana network. The bridge currently supports transfers from Stellar to Solana only.

## Features

- **QR Code Generation**: Users can scan a QR code with the Threefold Connect app to facilitate the transfer.

## Installation

To run this project locally, follow these steps:

1. **Clone the repository**:

```bash
    git clone https://github.com/threefoldtech/tfgrid-sdk-ts.git
    cd packages/stellar_solana_bridge/
```

2. **Install dependencies**:

```bash
   yarn install
```

3. **Run the application**:

```bash
    yarn dev
```

3. **Build the application**:

```bash
    yarn build
```
