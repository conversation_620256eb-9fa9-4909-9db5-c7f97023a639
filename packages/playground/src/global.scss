:root {
  --link-color: #5695ff;
}

.app-link {
  text-decoration: none;
  font-weight: bold;
  color: var(--link-color);
  cursor: pointer;
}

.fade-leave-active,
.fade-enter-active {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  pointer-events: none;

  transition: opacity 1s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.capitalize {
  text-transform: capitalize !important;
}

.v-btn {
  text-transform: capitalize !important;
  font-size: 1rem !important;
}

.version {
  position: absolute;
  bottom: 15px;
  right: 25px;
}

.v-tooltip > .v-overlay__content {
  /* background: var(--v-theme-surface); */
  border-color: rgba(var(--v-border-color), var(--v-border-opacity)) !important;
  border-width: thin !important;
  border-style: solid !important;
  z-index: 99;
  background-color: rgb(var(--v-theme-background)) !important;
  color: var(--v-theme-text) !important;
  font-weight: 500 !important;
}

a {
  color: #5695ff !important;
}

.v-list-item__prepend {
  width: 35px !important;
}

.v-list-item-title {
  font-size: 0.875rem;
}

.v-list-item--density-default.v-list-item--one-line {
  min-height: 40px;
}

.custom-toolbar-title {
  max-width: 17rem !important;
}
.mosha__toast__content-wrapper {
  margin-bottom: -2px;
}
.mosha__toast__slot-wrapper {
  margin-bottom: -2px;
}
.mosha__icon {
  margin-right: 6px !important;
  margin-top: 2px;
}

.mosha__icon__dark__warning {
  fill: #5d5d5d !important;
}

.mosha__icon__light__warning {
  fill: #5d5d5d !important;
}

.mosha__toast__content.dark__warning {
  color: #5d5d5d;
}

.mosha__toast__content.light__warning {
  color: #5d5d5d;
}

.mosha__toast__close-icon.dark__warning::before {
  color: #5d5d5d !important;
}

.mosha__toast__close-icon.light__warning::before {
  color: #5d5d5d !important;
}

.mosha__toast {
  font-size: 14px !important;
  font-weight: 600 !important;
}

.v-data-table-header__content {
  justify-content: center !important;
  text-align: center !important;
}

.v-data-table__td {
  text-align: center;
}
.v-data-table__tbody {
  text-align: center !important;
}

.font-14 {
  font-size: 14px !important;
}

.v-breadcrumbs-item--disabled {
  opacity: 1;
}

.v-btn {
  padding: 0rem 0.75rem !important;
}

.v-list-item-title,
.v-label--clickable,
.v-btn {
  font-size: 0.875rem !important;
}

.md-blockquote {
  margin-top: 40px;
  margin-bottom: 40px;
  padding: 30px;
  background: #2f4f4f2e;
  border-radius: 4px;
  border-left: 5px solid #1c81dd;
}

/* Update tables styles */
.v-table {
  border: thin solid rgba(var(--v-border-color), var(--v-border-opacity)) !important;
}

table {
  --v-table-header-height: 45px;

  thead {
    tr {
      th {
        span {
          font-weight: bold;
          text-wrap: nowrap;
        }

        border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));

        &:not(.v-data-table__th--sorted) i {
          opacity: 0.1 !important;
        }
      }

      &:first-of-type th:last-of-type {
        border-right: none;
      }
    }
  }

  tbody tr {
    td:not(:last-of-type) {
      border-right: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
    }
  }
}
