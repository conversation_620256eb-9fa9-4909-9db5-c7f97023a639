<template>
  <section class="mt-4">
    <template v-if="!disableNodeSelection">
      <h3 class="bg-primary pa-2 text-h6 rounded">Node Selection</h3>

      <TFRentalFilterSwitches
        v-model:rentedByMe="internalFilters.rentedByMe"
        v-model:dedicated="internalFilters.dedicated"
        v-model:certified="internalFilters.certified"
        v-model:hasGPU="internalFilters.hasGPU"
      />

      <p class="text-h6 mb-4 mt-2 ml-2">Choose a way to select Node</p>

      <v-radio-group v-model="wayToSelect" color="primary" inline>
        <InputTooltip
          align-center
          tooltip="Automatically select your node by filtering with Region, country, or farm name"
        >
          <v-radio label="Automated" value="automated"></v-radio>
        </InputTooltip>
        <InputTooltip align-center tooltip="Manually select your node by entering its id">
          <v-radio label="Manual" value="manual" class="ml-5"></v-radio>
        </InputTooltip>
      </v-radio-group>

      <div ref="input">
        <template v-if="wayToSelect === 'automated'">
          <TfSelectLocation v-model="location" title="Choose a Location" :status="NodeStatus.Up" />
          <TfSelectFarm :valid-filters="validFilters" :filters="internalFilters" :location="location" v-model="farm" />
          <TfAutoNodeSelector
            :selected-machines="selectedMachines"
            :nodes-lock="nodesLock"
            :valid-filters="validFilters"
            :filters="internalFilters"
            :location="location"
            :farm="farm"
            v-model="node"
            v-model:status="nodeStatus"
            :load-farm="loadFarm"
            :get-farm="getFarm"
          />
        </template>

        <TfManualNodeSelector
          :selected-machines="selectedMachines"
          :nodes-lock="nodesLock"
          :valid-filters="validFilters"
          :filters="internalFilters"
          v-model="node"
          v-model:status="nodeStatus"
          :load-farm="loadFarm"
          :get-farm="getFarm"
          v-else
        />
      </div>

      <VExpandTransition>
        <TfSelectGpu
          :node="node"
          :valid-node="nodeStatus === ValidatorStatus.Valid"
          v-model="gpuCards"
          v-model:status="gpuStatus"
          v-if="internalFilters.hasGPU"
        />
      </VExpandTransition>
    </template>

    <VExpandTransition>
      <TfDomainName
        :filters="internalFilters"
        :farm="farm"
        :hide-title="disableNodeSelection"
        v-model="domain"
        v-model:status="domainStatus"
        :use-fqdn="useFqdn"
        v-if="requireDomain"
        :interfaces="domainNameInterfaces"
      />
    </VExpandTransition>
  </section>
</template>

<script lang="ts">
import { type FarmInfo, Features, type GPUCardInfo, type NodeInfo } from "@threefold/grid_client";
import { type Farm, NodeStatus } from "@threefold/gridproxy_client";
import type AwaitLock from "await-lock";
import noop from "lodash/fp/noop.js";
import type { DeepPartial } from "utility-types";
import { computed, getCurrentInstance, onMounted, onUnmounted, type PropType, ref, watch } from "vue";

import { gridProxyClient } from "../../clients";
import { useWatchDeep } from "../../hooks";
import { useForm, ValidatorStatus } from "../../hooks/form_validator";
import type { InputValidatorService } from "../../hooks/input_validator";
import type {
  DomainInfo,
  NetworkFeatures,
  SelectedLocation,
  SelectedMachine,
  SelectionDetails,
  SelectionDetailsFilters,
  SelectionDetailsFiltersValidators,
} from "../../types/nodeSelector";
import { createSelectionDetailsFiltersValidator } from "../../utils/nodeSelector";
import TFRentalFilterSwitches from "../filters/TfRentalFilterSwitches.vue";
import TfAutoNodeSelector from "./TfAutoNodeSelector.vue";
import TfDomainName from "./TfDomainName.vue";
import TfManualNodeSelector from "./TfManualNodeSelector.vue";
import TfSelectFarm from "./TfSelectFarm.vue";
import TfSelectGpu from "./TfSelectGpu.vue";
import TfSelectLocation from "./TfSelectLocation.vue";
export default {
  name: "TfSelectionDetails",
  components: {
    TfSelectLocation,
    TfSelectFarm,
    TfAutoNodeSelector,
    TfManualNodeSelector,
    TfSelectGpu,
    TfDomainName,
    TFRentalFilterSwitches,
  },
  props: {
    modelValue: Object as PropType<SelectionDetails>,
    filters: {
      type: Object as PropType<SelectionDetailsFilters>,
      default: () => ({}),
    },
    filtersValidators: {
      type: Object as PropType<DeepPartial<SelectionDetailsFiltersValidators>>,
      default: () => ({}),
    },
    requireDomain: Boolean,
    disableNodeSelection: { type: Boolean, default: false },
    status: String as PropType<ValidatorStatus>,
    useFqdn: Boolean,
    selectedMachines: {
      type: Array as PropType<SelectedMachine[]>,
      default: () => [],
    },
    interfaces: {
      type: Array as PropType<NetworkFeatures[]>,
      default: () => [],
    },
    nodesLock: Object as PropType<AwaitLock>,
  },
  emits: {
    "update:model-value": (value: SelectionDetails) => true || value,
    "update:status": (value: ValidatorStatus) => true || value,
    "update:filters": (value: SelectionDetailsFilters) => true || value,
  },
  setup(props, ctx) {
    const input = ref<HTMLElement>();

    const internalFilters = ref({ ...props.filters });
    watch(
      internalFilters,
      val => {
        ctx.emit("update:filters", val);
      },
      { deep: true },
    );

    const filtersValidator = computed(() => createSelectionDetailsFiltersValidator(props.filtersValidators));
    const validFilters = computed(() => filtersValidator.value.safeParse(internalFilters.value).success);

    const wayToSelect = ref<"manual" | "automated">("automated");
    const location = ref<SelectedLocation>();
    const farm = ref<FarmInfo>();

    const node = ref<NodeInfo>();
    const nodeStatus = ref<ValidatorStatus>();

    const gpuCards = ref<GPUCardInfo[]>([]);
    const gpuStatus = ref<ValidatorStatus>();

    const domain = ref<DomainInfo>();
    const domainStatus = ref<ValidatorStatus>();

    const domainNameInterfaces = computed((): NetworkFeatures[] => {
      if (props.interfaces.length === 0) {
        const interfaces: NetworkFeatures[] = [];
        if (internalFilters.value.wireguard) interfaces.push(Features.wireguard);
        return interfaces;
      }
      return props.interfaces;
    });

    const loadedFarms = new Map<number, Farm>();
    async function loadFarm(farmId: number) {
      if (loadedFarms.has(farmId)) return loadedFarms.get(farmId)!;
      const [farm] = (await gridProxyClient.farms.list({ farmId })).data;
      loadedFarms.set(farmId, farm);
      return farm;
    }
    function getFarm(farmId: number) {
      return loadedFarms.get(farmId);
    }

    const selectionDetails = computed(() => {
      return {
        type: wayToSelect.value,
        validFilters: validFilters.value,
        node: node.value,
        farm: farm.value,
        gpuCards: gpuCards.value,
        location: location.value,
        domain: domain.value,
      } as SelectionDetails;
    });

    useWatchDeep(selectionDetails, value => ctx.emit("update:model-value", value), { immediate: true, deep: true });

    const { uid } = getCurrentInstance() as { uid: number };
    const form = useForm();
    const fakeService: InputValidatorService = {
      validate: () => Promise.resolve(true),
      setStatus: noop,
      reset: noop,
      status: ValidatorStatus.Init,
      error: null,
      $el: input,
      highlightOnError: true,
    };

    onMounted(() => form?.register(uid.toString(), fakeService));
    onUnmounted(() => form?.unregister(uid.toString()));

    const invalid = computed(() => {
      return (
        (!props.disableNodeSelection && nodeStatus.value === ValidatorStatus.Invalid) ||
        (props.requireDomain && domainStatus.value === ValidatorStatus.Invalid) ||
        (internalFilters.value.hasGPU && gpuStatus.value === ValidatorStatus.Invalid)
      );
    });

    const pending = computed(() => {
      return (
        (!props.disableNodeSelection && nodeStatus.value === ValidatorStatus.Pending) ||
        (props.requireDomain && domainStatus.value === ValidatorStatus.Pending) ||
        (internalFilters.value.hasGPU && gpuStatus.value === ValidatorStatus.Pending)
      );
    });

    const valid = computed(() => {
      return (
        (props.disableNodeSelection || (!props.disableNodeSelection && nodeStatus.value === ValidatorStatus.Valid)) &&
        (!props.requireDomain || (props.requireDomain && domainStatus.value === ValidatorStatus.Valid)) &&
        (!internalFilters.value.hasGPU || (internalFilters.value.hasGPU && gpuStatus.value === ValidatorStatus.Valid))
      );
    });

    const status = computed(() => {
      if (invalid.value) return ValidatorStatus.Invalid;
      if (pending.value) return ValidatorStatus.Pending;
      if (valid.value) return ValidatorStatus.Valid;
      return ValidatorStatus.Init;
    });

    watch(
      status,
      newStatus => {
        fakeService.status = newStatus;
        form?.updateStatus(uid.toString(), newStatus);
        ctx.emit("update:status", newStatus);
      },
      { deep: true, immediate: true },
    );

    return {
      input,
      validFilters,
      ValidatorStatus,
      wayToSelect,
      location,
      farm,
      node,
      nodeStatus,
      gpuCards,
      gpuStatus,
      domain,
      domainStatus,
      domainNameInterfaces,
      NodeStatus,
      loadFarm,
      getFarm,
      internalFilters,
    };
  },
};
</script>
