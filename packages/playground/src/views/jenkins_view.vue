<template>
  <view-layout>
    <Jenkins />

    <template #list>
      <TfDeploymentList title="<PERSON>" :project-name="name" />
    </template>
  </view-layout>
</template>

<script lang="ts">
import { ProjectName } from "../types";
import TfDeploymentList from "../weblets/tf_deployment_list.vue";
import <PERSON> from "../weblets/tf_jenkins.vue";

export default {
  name: "<PERSON>View",
  components: {
    <PERSON>,
    TfDeploymentList,
  },
  setup() {
    return { name: ProjectName.Jenkins };
  },
};
</script>
