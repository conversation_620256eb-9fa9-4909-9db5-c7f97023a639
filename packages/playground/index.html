<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Threefold Dashboard</title>
    <script src="/config.js"></script>
    <script defer src="https://cdn.jsdelivr.net/gh/threefoldtech/tf-map@0.0.4/dist/tf-map.js"></script>
    <script type="text/javascript">
      window.$crisp = [];
      window.CRISP_WEBSITE_ID = "1a5a5241-91cb-4a41-8323-5ba5ec574da0";
      (function () {
        d = document;
        s = d.createElement("script");
        s.src = "https://client.crisp.chat/l.js";
        s.async = 1;
        d.getElementsByTagName("head")[0].appendChild(s);
      })();
    </script>
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.12.2/lottie.min.js"
      integrity="sha512-jEnuDt6jfecCjthQAJ+ed0MTVA++5ZKmlUcmDGBv2vUI/REn6FuIdixLNnQT+vKusE2hhTk2is3cFvv5wA+Sgg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>
    <link rel="stylesheet" href="/loader/loader.css" />
  </head>
  <body>
    <div class="app-loader-dialog active">
      <div class="app-loader-container">
        <div style="width: 50px" id="animation-container"></div>
        <script>
          var animation = bodymovin.loadAnimation({
            container: document.getElementById("animation-container"),
            path: "/images/tfloading.json",
            renderer: "svg",
            loop: true,
            autoplay: true,
            name: "Demo Animation",
          });
        </script>
        <img class="logo_load" src="/images/logo_text.png" alt="logo" />
        <span class="app-loader-msg active">Loading dashboard. Please wait...</span>
        <span class="app-monitor-msg"></span>
        <div class="app-monitor-container">
          <ul class="app-monitor-status">
          </ul>
        </div>
        <button class="app-loader-refresh">Refresh</button>
      </div>
    </div>
    <div id="app"></div>
    <div id="modals"></div>
    <script defer type="module" src="/src/main.ts"></script>
    <script src="/loader/loader.js"></script>
  </body>
</html>
<style>
  .logo_load{
    margin-top: 20px;
    width: 160px;
  }
</style>
