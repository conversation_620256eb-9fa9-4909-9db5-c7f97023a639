import { describe, expect, it } from "vitest";

import { ipToLong, longToIp } from "../../src/utils/ip";
import { isPublicIP } from "../../src/utils/validators";

describe("ipToLong", () => {
  it('should convert IPv4 "0.0.0.0" to 0', () => {
    expect(ipToLong("0.0.0.0")).toBe(0n);
  });

  it('should convert IPv4 "***************" to 4294967295', () => {
    expect(ipToLong("***************")).toBe(4294967295n);
  });

  it('should convert IPv4 "***********" to 3232235777', () => {
    expect(ipToLong("***********")).toBe(3232235777n);
  });

  it('should convert IPv4 "127.0.0.1" to 2130706433', () => {
    expect(ipToLong("127.0.0.1")).toBe(2130706433n);
  });

  it("should handle invalid input gracefully", () => {
    expect(() => ipToLong("256.100.50.25")).toThrowError();
    expect(() => ipToLong("192.168.1")).toThrowError();
    expect(() => ipToLong("not.an.ip")).toThrowError();
    expect(() => ipToLong("***************")).toThrowError();
  });
});
describe("longToIp", () => {
  it('should convert 0 to "0.0.0.0"', () => {
    expect(longToIp(0)).toBe("0.0.0.0");
  });

  it('should convert 4294967295 to "***************"', () => {
    expect(longToIp(4294967295)).toBe("***************");
  });

  it('should convert 3232235777 to "***********"', () => {
    expect(longToIp(3232235777)).toBe("***********");
  });

  it('should convert 2130706433 to "127.0.0.1"', () => {
    expect(longToIp(2130706433)).toBe("127.0.0.1");
  });

  it('should convert 4194308 to "*******"', () => {
    expect(longToIp(4194308)).toBe("********");
  });

  it('should convert 2886729728 to "**********"', () => {
    expect(longToIp(2886729728)).toBe("**********");
  });

  it("should correctly convert long to ip", () => {
    const ip = "***********";
    const long = 3232235777;
    expect(longToIp(long)).toBe(ip);
  });
});

describe("isPublicIP", () => {
  const validPublicIPs = ["*******", "*******/24", "**********", "***********", "***********", "*************"];

  const invalidPublicIPs = [
    // Private IPs
    "********",
    "**********",
    "***********",
    // Loopback
    "127.0.0.1",
    // Link-local
    "***********",
    // Multicast
    "*********",
    "**************",
    "***************",
    // Reserved
    "*********",
    "***************",
    // Documentation and example ranges
    "*********", // TEST-NET-1
    "************", // TEST-NET-2
    "***********", // TEST-NET-3
  ];

  it.each(validPublicIPs)("should return undefined for valid public IP %s", ip => {
    expect(isPublicIP()(ip)).toBeUndefined();
  });

  it.each(invalidPublicIPs)("should return an error message for invalid public IP %s", ip => {
    expect(isPublicIP()(ip)).toEqual({ message: "IP is not public" });
  });

  it("should return a custom error message when provided", () => {
    const customMessage = "Custom error message";
    expect(isPublicIP(customMessage)("********")).toEqual({ message: customMessage });
  });
});
