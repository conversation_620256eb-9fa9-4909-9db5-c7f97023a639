{"name": "@threefold/playground", "version": "2.8.0-rc4", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check build-only", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "dependencies": {"@mdi/font": "^7.2.96", "@sentry/vue": "^8.19.0", "@threefold/graphql_client": "2.8.0-rc4", "@threefold/grid_client": "2.8.0-rc4", "@threefold/gridproxy_client": "2.8.0-rc4", "@threefold/monitoring": "2.8.0-rc4", "@threefold/types": "2.8.0-rc4", "@types/ip": "^1.1.3", "@types/md5": "^2.3.5", "await-lock": "^2.2.2", "bip39": "^3.1.0", "chart.js": "^4.4.0", "cidr-tools": "^5.1.4", "country-code-lookup": "^0.1.2", "cryptr": "^6.2.0", "decimal.js": "^10.4.3", "front-matter": "^4.0.2", "get-ip-range": "^4.0.1", "highlight.js": "^11.7.0", "ip": "^2.0.1", "jspdf": "^3.0.1", "jspdf-autotable": "^3.8.2", "lodash": "^4.17.21", "logger-interceptor": "1.0.0", "marked": "^5.0.4", "md5": "^2.3.0", "moment": "^2.29.4", "mosha-vue-toastify": "^1.0.23", "pinia": "^2.0.32", "qrcode": "^1.5.1", "url-join": "^5.0.0", "validator": "^13.9.0", "vue": "^3.2.47", "vue-chartjs": "^5.2.0", "vue-router": "^4.1.6", "vue3-virtual-scroller": "^0.2.3", "vuetify": "^3.5.17", "web-ssh-keygen": "^0.1.2", "zod": "^3.22.4"}, "devDependencies": {"@types/chart.js": "^2.9.38", "@types/jsdom": "^21.1.0", "@types/lodash": "^4.14.192", "@types/marked": "^5.0.0", "@types/md5": "^2.3.5", "@types/node": "^18.14.2", "@types/qrcode": "^1.5.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.1.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^2.3.0", "@vue/tsconfig": "^0.1.3", "jsdom": "^21.1.0", "npm-run-all": "^4.1.5", "sass": "^1.60.0", "start-server-and-test": "^2.0.0", "typescript": "~4.8.4", "utility-types": "^3.10.0", "vite": "^4.1.4", "vite-plugin-node-polyfills": "^0.7.0", "vitest": "^0.29.1", "vue-tsc": "^1.2.0"}}