# Default values for chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ghcr.io/threefoldtech/playground
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "2.8.0-rc4"
  
  env:
    - name: "MODE"
      value: ""
    - name: "GRAPHQL_URL"
      value: ""
    - name: "BRIDGE_TFT_ADDRESS"
      value: ""
    - name: "GRIDPROXY_URL"
      value: ""
    - name: "SUBSTRATE_URL"
      value: ""
    - name: "ACTIVATION_SERVICE_URL"
      value: ""
    - name: "RELAY_DOMAIN"
      value: ""
    - name: "STELLAR_NETWORK"
      value: ""
    - name: "SENTRY_DSN"
      value: ""
    - name: "ENABLE_TELEMETRY"
      value: false


imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "playground-charts"

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 2000

securityContext:
  {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  className: ""
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/ingress.class: nginx
    kubernetes.io/tls-acme: "true"
  hosts:
    - host: play.dev.grid.tf
      paths:
        - path: /
          pathType: ImplementationSpecific

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
