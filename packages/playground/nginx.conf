server {

  listen 80;
  
  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;

     #proxy_pass http://***************:3030/;
  }


  error_page   500 502 503 504  /50x.html;

  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}


upstream backend {

  # nginx as a load balancer with proxy
  ip_hash;
  server ***********:31300 weight=5;
  server ***********:31301 weight=1;
  server ***********:31302 max_fails=3 fail_timeout=30s weight=1;

}


proxy_cache_path /etc/nginx/cache keys_zone=my_cache:10m max_size=100m inactive=60m use_temp_path=off;
# nginx as a reverse proxy
# this will route our frontend requests to the backend on port 4008
server {
  listen 9090;

  location / {
        proxy_cache my_cache;
        proxy_cache_valid 200 30s;
        proxy_cache_methods GET HEAD POST;
        proxy_cache_min_uses 1;
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
        proxy_pass http://backend;
        proxy_redirect     off;
        proxy_set_header   Host $host;
        proxy_set_header   X-Real-IP $remote_addr;
        proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header   X-Forwarded-Host $server_name;
  }

}