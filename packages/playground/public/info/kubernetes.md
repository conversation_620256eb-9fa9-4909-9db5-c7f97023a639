---
title: Kubernetes
---

Kubernetes is the standard container orchestration tool. On the TF grid, Kubernetes clusters can be deployed out of the box. We have implemented K3S, a full-blown Kubernetes offering that uses only half of the memory footprint. It is packaged as a single binary and made more lightweight to run workloads in resource-constrained locations (fits e.g. IoT, edge, ARM workloads). For more details, check [Kubernetes documentation](https://www.manual.grid.tf/documentation/dashboard/solutions/k8s.html).
