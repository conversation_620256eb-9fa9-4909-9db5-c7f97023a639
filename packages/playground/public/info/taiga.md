---
title: <PERSON>ga
---

Taiga is a project management application that can handle both simple and complex projects for startups, software developers, and other target teams. It tracks the progress of a project. With Taiga, you can use either Kanban or Scrum template, or both. Backlogs are shown as a running list of all features and User Stories added to the project.[5] Taiga is available in more than 20 different languages.

Taiga integrates video conferencing functions with the use of third party services like Talky.io, Jitsi and Whereby Group and private chat is done via Slack. For more details, check [Taiga documentation](https://www.manual.grid.tf/documentation/dashboard/solutions/taiga.html).
