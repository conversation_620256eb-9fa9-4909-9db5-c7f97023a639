---
title: Contracts
---

For more details about Contract Types, Billing Cycle & Grace Period, check [Contract Documentation](https://www.manual.grid.tf/documentation/developers/tfchain/tfchain.html)
<br />

To explore further contract details, check [Node Contract Documentation](https://www.manual.grid.tf/documentation/dashboard/deploy/your_contracts.html)
<br />

- Contract State provides you with the following information:

  - An indicator of whether the contract is created or in the grace period
  - The number of tokens needed to fund contracts in the grace period to resume the workload
  - Contract expiration date

  <br />

- The Billing Rate: The Billing Rate (i.e contract cost per hour)
- Solution type and name (if provided by contract creator)
- Full contracts details in JSON format
