---
title: TFRobot
---

TFRobot is a command line interface tool that offers simultaneous mass deployment of groups of VMs on the ThreeFold Grid, with support of multiple retries for failed deployments, and customizable configurations, where you can define node groups, VMs groups and other configurations through a YAML or a JSON file. For more details, check [TFRobot documentation](https://manual.grid.tf/documentation/dashboard/solutions/tfrobot.html).
