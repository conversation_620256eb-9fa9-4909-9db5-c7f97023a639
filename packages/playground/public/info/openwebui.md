---
title: Open WebUI
---

Open WebUI is an extensible, feature-rich, and user-friendly self-hosted AI platform designed to operate entirely offline. It supports various LLM runners like Ollama and OpenAI-compatible APIs, with built-in inference engine for RAG, making it a powerful AI deployment solution. For more details, check [Open WebUI documentation](https://www.manual.grid.tf/documentation/dashboard/solutions/openwebui.html).
