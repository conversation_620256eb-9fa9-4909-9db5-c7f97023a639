window.env = {
  WALLET_KEY: "wallet.v1",
  NETWORK: "dev",
  GRAPHQL_STACKS: ["https://graphql.dev.grid.tf/graphql", "https://graphql.02.dev.grid.tf/graphql"],
  GRIDPROXY_STACKS: ["https://gridproxy.dev.grid.tf", "https://gridproxy.02.dev.grid.tf"],
  SUBSTRATE_STACKS: ["wss://tfchain.dev.grid.tf/ws", "wss://tfchain.02.dev.grid.tf/ws"],
  ACTIVATION_SERVICE_STACKS: [
    "https://activation.dev.grid.tf/activation/activate",
    "https://activation.02.dev.grid.tf/activation/activate",
  ],
  RELAY_STACKS: ["wss://relay.dev.grid.tf", "wss://relay.02.dev.grid.tf"],
  BRIDGE_TFT_ADDRESS: "GDHJP6TF3UXYXTNEZ2P36J5FH7W4BJJQ4AYYAXC66I2Q2AH5B6O6BCFG",
  STELLAR_NETWORK: "test",
  STELLAR_HORIZON_URL: "https://horizon-testnet.stellar.org",
  TFT_ASSET_ISSUER: "GA47YZA3PKFUZMPLQ3B5F2E3CJIB57TGGU7SPCQT2WAEYKN766PWIMB3",
  MINTING_URL: "https://alpha.minting.tfchain.grid.tf",
  KYC_URL: "https://kyc.dev.grid.tf",
  STATS_STACKS: ["https://stats.dev.grid.tf", "https://stats.02.dev.grid.tf"],
  TIMEOUT: +"10000",
  PAGE_SIZE: +"20",
  MANUAL_URL: "https://www.manual.grid.tf",
  SENTRY_DSN: "https://<EMAIL>/2",
  ENABLE_TELEMETRY: "false",
};
