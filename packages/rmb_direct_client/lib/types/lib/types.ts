/**
 * Generated by the protoc-gen-ts.  DO NOT EDIT!
 * compiler version: 3.21.12
 * source: lib/types.proto
 * git: https://github.com/thesayyn/protoc-gen-ts */
import * as pb_1 from "google-protobuf";
export class Request extends pb_1.Message {
  #one_of_decls: number[][] = [];
  constructor(
    data?:
      | any[]
      | {
          command?: string;
        },
  ) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
      if ("command" in data && data.command != undefined) {
        this.command = data.command;
      }
    }
  }
  get command() {
    return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
  }
  set command(value: string) {
    pb_1.Message.setField(this, 1, value);
  }
  static fromObject(data: { command?: string }): Request {
    const message = new Request({});
    if (data.command != null) {
      message.command = data.command;
    }
    return message;
  }
  toObject() {
    const data: {
      command?: string;
    } = {};
    if (this.command != null) {
      data.command = this.command;
    }
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (this.command.length) writer.writeString(1, this.command);
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Request {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Request();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        case 1:
          message.command = reader.readString();
          break;
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Request {
    return Request.deserialize(bytes);
  }
}
export class Response extends pb_1.Message {
  #one_of_decls: number[][] = [];
  constructor(data?: any[] | {}) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
    }
  }
  static fromObject(data: {}): Response {
    const message = new Response({});
    return message;
  }
  toObject() {
    const data: {} = {};
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Response {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Response();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Response {
    return Response.deserialize(bytes);
  }
}
export class Error extends pb_1.Message {
  #one_of_decls: number[][] = [];
  constructor(
    data?:
      | any[]
      | {
          code?: number;
          message?: string;
        },
  ) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
      if ("code" in data && data.code != undefined) {
        this.code = data.code;
      }
      if ("message" in data && data.message != undefined) {
        this.message = data.message;
      }
    }
  }
  get code() {
    return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
  }
  set code(value: number) {
    pb_1.Message.setField(this, 1, value);
  }
  get message() {
    return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
  }
  set message(value: string) {
    pb_1.Message.setField(this, 2, value);
  }
  static fromObject(data: { code?: number; message?: string }): Error {
    const message = new Error({});
    if (data.code != null) {
      message.code = data.code;
    }
    if (data.message != null) {
      message.message = data.message;
    }
    return message;
  }
  toObject() {
    const data: {
      code?: number;
      message?: string;
    } = {};
    if (this.code != null) {
      data.code = this.code;
    }
    if (this.message != null) {
      data.message = this.message;
    }
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (this.code != 0) writer.writeUint32(1, this.code);
    if (this.message.length) writer.writeString(2, this.message);
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Error {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Error();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        case 1:
          message.code = reader.readUint32();
          break;
        case 2:
          message.message = reader.readString();
          break;
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Error {
    return Error.deserialize(bytes);
  }
}
export class Address extends pb_1.Message {
  #one_of_decls: number[][] = [[2]];
  constructor(
    data?:
      | any[]
      | ({
          twin?: number;
        } & {
          connection?: string;
        }),
  ) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
      if ("twin" in data && data.twin != undefined) {
        this.twin = data.twin;
      }
      if ("connection" in data && data.connection != undefined) {
        this.connection = data.connection;
      }
    }
  }
  get twin() {
    return pb_1.Message.getFieldWithDefault(this, 1, 0) as number;
  }
  set twin(value: number) {
    pb_1.Message.setField(this, 1, value);
  }
  get connection() {
    return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
  }
  set connection(value: string) {
    pb_1.Message.setOneofField(this, 2, this.#one_of_decls[0], value);
  }
  get has_connection() {
    return pb_1.Message.getField(this, 2) != null;
  }
  get _connection() {
    const cases: {
      [index: number]: "none" | "connection";
    } = {
      0: "none",
      2: "connection",
    };
    return cases[pb_1.Message.computeOneofCase(this, [2])];
  }
  static fromObject(data: { twin?: number; connection?: string }): Address {
    const message = new Address({});
    if (data.twin != null) {
      message.twin = data.twin;
    }
    if (data.connection != null) {
      message.connection = data.connection;
    }
    return message;
  }
  toObject() {
    const data: {
      twin?: number;
      connection?: string;
    } = {};
    if (this.twin != null) {
      data.twin = this.twin;
    }
    if (this.connection != null) {
      data.connection = this.connection;
    }
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (this.twin != 0) writer.writeUint32(1, this.twin);
    if (this.has_connection) writer.writeString(2, this.connection);
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Address {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Address();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        case 1:
          message.twin = reader.readUint32();
          break;
        case 2:
          message.connection = reader.readString();
          break;
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Address {
    return Address.deserialize(bytes);
  }
}
export class Ping extends pb_1.Message {
  #one_of_decls: number[][] = [];
  constructor(data?: any[] | {}) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
    }
  }
  static fromObject(data: {}): Ping {
    const message = new Ping({});
    return message;
  }
  toObject() {
    const data: {} = {};
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Ping {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Ping();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Ping {
    return Ping.deserialize(bytes);
  }
}
export class Pong extends pb_1.Message {
  #one_of_decls: number[][] = [];
  constructor(data?: any[] | {}) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
    }
  }
  static fromObject(data: {}): Pong {
    const message = new Pong({});
    return message;
  }
  toObject() {
    const data: {} = {};
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Pong {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Pong();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Pong {
    return Pong.deserialize(bytes);
  }
}
export class Envelope extends pb_1.Message {
  #one_of_decls: number[][] = [[7, 8, 12, 15, 16], [13, 14], [2], [9], [10], [11]];
  constructor(
    data?:
      | any[]
      | ({
          uid?: string;
          timestamp?: number;
          expiration?: number;
          source?: Address;
          destination?: Address;
          relays?: string[];
        } & (
          | (
              | {
                  request?: Request;
                  response?: never;
                  error?: never;
                  ping?: never;
                  pong?: never;
                }
              | {
                  request?: never;
                  response?: Response;
                  error?: never;
                  ping?: never;
                  pong?: never;
                }
              | {
                  request?: never;
                  response?: never;
                  error?: Error;
                  ping?: never;
                  pong?: never;
                }
              | {
                  request?: never;
                  response?: never;
                  error?: never;
                  ping?: Ping;
                  pong?: never;
                }
              | {
                  request?: never;
                  response?: never;
                  error?: never;
                  ping?: never;
                  pong?: Pong;
                }
            )
          | (
              | {
                  plain?: Uint8Array;
                  cipher?: never;
                }
              | {
                  plain?: never;
                  cipher?: Uint8Array;
                }
            )
          | {
              tags?: string;
            }
          | {
              signature?: Uint8Array;
            }
          | {
              schema?: string;
            }
          | {
              federation?: string;
            }
        )),
  ) {
    super();
    pb_1.Message.initialize(this, Array.isArray(data) ? data : [], 0, -1, [17], this.#one_of_decls);
    if (!Array.isArray(data) && typeof data == "object") {
      if ("uid" in data && data.uid != undefined) {
        this.uid = data.uid;
      }
      if ("tags" in data && data.tags != undefined) {
        this.tags = data.tags;
      }
      if ("timestamp" in data && data.timestamp != undefined) {
        this.timestamp = data.timestamp;
      }
      if ("expiration" in data && data.expiration != undefined) {
        this.expiration = data.expiration;
      }
      if ("source" in data && data.source != undefined) {
        this.source = data.source;
      }
      if ("destination" in data && data.destination != undefined) {
        this.destination = data.destination;
      }
      if ("request" in data && data.request != undefined) {
        this.request = data.request;
      }
      if ("response" in data && data.response != undefined) {
        this.response = data.response;
      }
      if ("error" in data && data.error != undefined) {
        this.error = data.error;
      }
      if ("ping" in data && data.ping != undefined) {
        this.ping = data.ping;
      }
      if ("pong" in data && data.pong != undefined) {
        this.pong = data.pong;
      }
      if ("signature" in data && data.signature != undefined) {
        this.signature = data.signature;
      }
      if ("schema" in data && data.schema != undefined) {
        this.schema = data.schema;
      }
      if ("federation" in data && data.federation != undefined) {
        this.federation = data.federation;
      }
      if ("plain" in data && data.plain != undefined) {
        this.plain = data.plain;
      }
      if ("cipher" in data && data.cipher != undefined) {
        this.cipher = data.cipher;
      }
      if ("relays" in data && data.relays != undefined) {
        this.relays = data.relays;
      }
    }
  }
  get uid() {
    return pb_1.Message.getFieldWithDefault(this, 1, "") as string;
  }
  set uid(value: string) {
    pb_1.Message.setField(this, 1, value);
  }
  get tags() {
    return pb_1.Message.getFieldWithDefault(this, 2, "") as string;
  }
  set tags(value: string) {
    pb_1.Message.setOneofField(this, 2, this.#one_of_decls[2], value);
  }
  get has_tags() {
    return pb_1.Message.getField(this, 2) != null;
  }
  get timestamp() {
    return pb_1.Message.getFieldWithDefault(this, 3, 0) as number;
  }
  set timestamp(value: number) {
    pb_1.Message.setField(this, 3, value);
  }
  get expiration() {
    return pb_1.Message.getFieldWithDefault(this, 4, 0) as number;
  }
  set expiration(value: number) {
    pb_1.Message.setField(this, 4, value);
  }
  get source() {
    return pb_1.Message.getWrapperField(this, Address, 5) as Address;
  }
  set source(value: Address) {
    pb_1.Message.setWrapperField(this, 5, value);
  }
  get has_source() {
    return pb_1.Message.getField(this, 5) != null;
  }
  get destination() {
    return pb_1.Message.getWrapperField(this, Address, 6) as Address;
  }
  set destination(value: Address) {
    pb_1.Message.setWrapperField(this, 6, value);
  }
  get has_destination() {
    return pb_1.Message.getField(this, 6) != null;
  }
  get request() {
    return pb_1.Message.getWrapperField(this, Request, 7) as Request;
  }
  set request(value: Request) {
    pb_1.Message.setOneofWrapperField(this, 7, this.#one_of_decls[0], value);
  }
  get has_request() {
    return pb_1.Message.getField(this, 7) != null;
  }
  get response() {
    return pb_1.Message.getWrapperField(this, Response, 8) as Response;
  }
  set response(value: Response) {
    pb_1.Message.setOneofWrapperField(this, 8, this.#one_of_decls[0], value);
  }
  get has_response() {
    return pb_1.Message.getField(this, 8) != null;
  }
  get error() {
    return pb_1.Message.getWrapperField(this, Error, 12) as Error;
  }
  set error(value: Error) {
    pb_1.Message.setOneofWrapperField(this, 12, this.#one_of_decls[0], value);
  }
  get has_error() {
    return pb_1.Message.getField(this, 12) != null;
  }
  get ping() {
    return pb_1.Message.getWrapperField(this, Ping, 15) as Ping;
  }
  set ping(value: Ping) {
    pb_1.Message.setOneofWrapperField(this, 15, this.#one_of_decls[0], value);
  }
  get has_ping() {
    return pb_1.Message.getField(this, 15) != null;
  }
  get pong() {
    return pb_1.Message.getWrapperField(this, Pong, 16) as Pong;
  }
  set pong(value: Pong) {
    pb_1.Message.setOneofWrapperField(this, 16, this.#one_of_decls[0], value);
  }
  get has_pong() {
    return pb_1.Message.getField(this, 16) != null;
  }
  get signature() {
    return pb_1.Message.getFieldWithDefault(this, 9, new Uint8Array(0)) as Uint8Array;
  }
  set signature(value: Uint8Array) {
    pb_1.Message.setOneofField(this, 9, this.#one_of_decls[3], value);
  }
  get has_signature() {
    return pb_1.Message.getField(this, 9) != null;
  }
  get schema() {
    return pb_1.Message.getFieldWithDefault(this, 10, "") as string;
  }
  set schema(value: string) {
    pb_1.Message.setOneofField(this, 10, this.#one_of_decls[4], value);
  }
  get has_schema() {
    return pb_1.Message.getField(this, 10) != null;
  }
  get federation() {
    return pb_1.Message.getFieldWithDefault(this, 11, "") as string;
  }
  set federation(value: string) {
    pb_1.Message.setOneofField(this, 11, this.#one_of_decls[5], value);
  }
  get has_federation() {
    return pb_1.Message.getField(this, 11) != null;
  }
  get plain() {
    return pb_1.Message.getFieldWithDefault(this, 13, new Uint8Array(0)) as Uint8Array;
  }
  set plain(value: Uint8Array) {
    pb_1.Message.setOneofField(this, 13, this.#one_of_decls[1], value);
  }
  get has_plain() {
    return pb_1.Message.getField(this, 13) != null;
  }
  get cipher() {
    return pb_1.Message.getFieldWithDefault(this, 14, new Uint8Array(0)) as Uint8Array;
  }
  set cipher(value: Uint8Array) {
    pb_1.Message.setOneofField(this, 14, this.#one_of_decls[1], value);
  }
  get has_cipher() {
    return pb_1.Message.getField(this, 14) != null;
  }
  get relays() {
    return pb_1.Message.getFieldWithDefault(this, 17, []) as string[];
  }
  set relays(value: string[]) {
    pb_1.Message.setField(this, 17, value);
  }
  get message() {
    const cases: {
      [index: number]: "none" | "request" | "response" | "error" | "ping" | "pong";
    } = {
      0: "none",
      7: "request",
      8: "response",
      12: "error",
      15: "ping",
      16: "pong",
    };
    return cases[pb_1.Message.computeOneofCase(this, [7, 8, 12, 15, 16])];
  }
  get payload() {
    const cases: {
      [index: number]: "none" | "plain" | "cipher";
    } = {
      0: "none",
      13: "plain",
      14: "cipher",
    };
    return cases[pb_1.Message.computeOneofCase(this, [13, 14])];
  }
  get _tags() {
    const cases: {
      [index: number]: "none" | "tags";
    } = {
      0: "none",
      2: "tags",
    };
    return cases[pb_1.Message.computeOneofCase(this, [2])];
  }
  get _signature() {
    const cases: {
      [index: number]: "none" | "signature";
    } = {
      0: "none",
      9: "signature",
    };
    return cases[pb_1.Message.computeOneofCase(this, [9])];
  }
  get _schema() {
    const cases: {
      [index: number]: "none" | "schema";
    } = {
      0: "none",
      10: "schema",
    };
    return cases[pb_1.Message.computeOneofCase(this, [10])];
  }
  get _federation() {
    const cases: {
      [index: number]: "none" | "federation";
    } = {
      0: "none",
      11: "federation",
    };
    return cases[pb_1.Message.computeOneofCase(this, [11])];
  }
  static fromObject(data: {
    uid?: string;
    tags?: string;
    timestamp?: number;
    expiration?: number;
    source?: ReturnType<typeof Address.prototype.toObject>;
    destination?: ReturnType<typeof Address.prototype.toObject>;
    request?: ReturnType<typeof Request.prototype.toObject>;
    response?: ReturnType<typeof Response.prototype.toObject>;
    error?: ReturnType<typeof Error.prototype.toObject>;
    ping?: ReturnType<typeof Ping.prototype.toObject>;
    pong?: ReturnType<typeof Pong.prototype.toObject>;
    signature?: Uint8Array;
    schema?: string;
    federation?: string;
    plain?: Uint8Array;
    cipher?: Uint8Array;
    relays?: string[];
  }): Envelope {
    const message = new Envelope({});
    if (data.uid != null) {
      message.uid = data.uid;
    }
    if (data.tags != null) {
      message.tags = data.tags;
    }
    if (data.timestamp != null) {
      message.timestamp = data.timestamp;
    }
    if (data.expiration != null) {
      message.expiration = data.expiration;
    }
    if (data.source != null) {
      message.source = Address.fromObject(data.source);
    }
    if (data.destination != null) {
      message.destination = Address.fromObject(data.destination);
    }
    if (data.request != null) {
      message.request = Request.fromObject(data.request);
    }
    if (data.response != null) {
      message.response = Response.fromObject(data.response);
    }
    if (data.error != null) {
      message.error = Error.fromObject(data.error);
    }
    if (data.ping != null) {
      message.ping = Ping.fromObject(data.ping);
    }
    if (data.pong != null) {
      message.pong = Pong.fromObject(data.pong);
    }
    if (data.signature != null) {
      message.signature = data.signature;
    }
    if (data.schema != null) {
      message.schema = data.schema;
    }
    if (data.federation != null) {
      message.federation = data.federation;
    }
    if (data.plain != null) {
      message.plain = data.plain;
    }
    if (data.cipher != null) {
      message.cipher = data.cipher;
    }
    if (data.relays != null) {
      message.relays = data.relays;
    }
    return message;
  }
  toObject() {
    const data: {
      uid?: string;
      tags?: string;
      timestamp?: number;
      expiration?: number;
      source?: ReturnType<typeof Address.prototype.toObject>;
      destination?: ReturnType<typeof Address.prototype.toObject>;
      request?: ReturnType<typeof Request.prototype.toObject>;
      response?: ReturnType<typeof Response.prototype.toObject>;
      error?: ReturnType<typeof Error.prototype.toObject>;
      ping?: ReturnType<typeof Ping.prototype.toObject>;
      pong?: ReturnType<typeof Pong.prototype.toObject>;
      signature?: Uint8Array;
      schema?: string;
      federation?: string;
      plain?: Uint8Array;
      cipher?: Uint8Array;
      relays?: string[];
    } = {};
    if (this.uid != null) {
      data.uid = this.uid;
    }
    if (this.tags != null) {
      data.tags = this.tags;
    }
    if (this.timestamp != null) {
      data.timestamp = this.timestamp;
    }
    if (this.expiration != null) {
      data.expiration = this.expiration;
    }
    if (this.source != null) {
      data.source = this.source.toObject();
    }
    if (this.destination != null) {
      data.destination = this.destination.toObject();
    }
    if (this.request != null) {
      data.request = this.request.toObject();
    }
    if (this.response != null) {
      data.response = this.response.toObject();
    }
    if (this.error != null) {
      data.error = this.error.toObject();
    }
    if (this.ping != null) {
      data.ping = this.ping.toObject();
    }
    if (this.pong != null) {
      data.pong = this.pong.toObject();
    }
    if (this.signature != null) {
      data.signature = this.signature;
    }
    if (this.schema != null) {
      data.schema = this.schema;
    }
    if (this.federation != null) {
      data.federation = this.federation;
    }
    if (this.plain != null) {
      data.plain = this.plain;
    }
    if (this.cipher != null) {
      data.cipher = this.cipher;
    }
    if (this.relays != null) {
      data.relays = this.relays;
    }
    return data;
  }
  serialize(): Uint8Array;
  serialize(w: pb_1.BinaryWriter): void;
  serialize(w?: pb_1.BinaryWriter): Uint8Array | void {
    const writer = w || new pb_1.BinaryWriter();
    if (this.uid.length) writer.writeString(1, this.uid);
    if (this.has_tags) writer.writeString(2, this.tags);
    if (this.timestamp != 0) writer.writeUint64(3, this.timestamp);
    if (this.expiration != 0) writer.writeUint64(4, this.expiration);
    if (this.has_source) writer.writeMessage(5, this.source, () => this.source.serialize(writer));
    if (this.has_destination) writer.writeMessage(6, this.destination, () => this.destination.serialize(writer));
    if (this.has_request) writer.writeMessage(7, this.request, () => this.request.serialize(writer));
    if (this.has_response) writer.writeMessage(8, this.response, () => this.response.serialize(writer));
    if (this.has_error) writer.writeMessage(12, this.error, () => this.error.serialize(writer));
    if (this.has_ping) writer.writeMessage(15, this.ping, () => this.ping.serialize(writer));
    if (this.has_pong) writer.writeMessage(16, this.pong, () => this.pong.serialize(writer));
    if (this.has_signature) writer.writeBytes(9, this.signature);
    if (this.has_schema) writer.writeString(10, this.schema);
    if (this.has_federation) writer.writeString(11, this.federation);
    if (this.has_plain) writer.writeBytes(13, this.plain);
    if (this.has_cipher) writer.writeBytes(14, this.cipher);
    if (this.relays.length) writer.writeRepeatedString(17, this.relays);
    if (!w) return writer.getResultBuffer();
  }
  static deserialize(bytes: Uint8Array | pb_1.BinaryReader): Envelope {
    const reader = bytes instanceof pb_1.BinaryReader ? bytes : new pb_1.BinaryReader(bytes),
      message = new Envelope();
    while (reader.nextField()) {
      if (reader.isEndGroup()) break;
      switch (reader.getFieldNumber()) {
        case 1:
          message.uid = reader.readString();
          break;
        case 2:
          message.tags = reader.readString();
          break;
        case 3:
          message.timestamp = reader.readUint64();
          break;
        case 4:
          message.expiration = reader.readUint64();
          break;
        case 5:
          reader.readMessage(message.source, () => (message.source = Address.deserialize(reader)));
          break;
        case 6:
          reader.readMessage(message.destination, () => (message.destination = Address.deserialize(reader)));
          break;
        case 7:
          reader.readMessage(message.request, () => (message.request = Request.deserialize(reader)));
          break;
        case 8:
          reader.readMessage(message.response, () => (message.response = Response.deserialize(reader)));
          break;
        case 12:
          reader.readMessage(message.error, () => (message.error = Error.deserialize(reader)));
          break;
        case 15:
          reader.readMessage(message.ping, () => (message.ping = Ping.deserialize(reader)));
          break;
        case 16:
          reader.readMessage(message.pong, () => (message.pong = Pong.deserialize(reader)));
          break;
        case 9:
          message.signature = reader.readBytes();
          break;
        case 10:
          message.schema = reader.readString();
          break;
        case 11:
          message.federation = reader.readString();
          break;
        case 13:
          message.plain = reader.readBytes();
          break;
        case 14:
          message.cipher = reader.readBytes();
          break;
        case 17:
          pb_1.Message.addToRepeatedField(message, 17, reader.readString());
          break;
        default:
          reader.skipField();
      }
    }
    return message;
  }
  serializeBinary(): Uint8Array {
    return this.serialize();
  }
  static deserializeBinary(bytes: Uint8Array): Envelope {
    return Envelope.deserialize(bytes);
  }
}
