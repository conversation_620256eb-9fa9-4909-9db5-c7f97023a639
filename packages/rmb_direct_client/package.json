{"name": "@threefold/rmb_direct_client", "version": "2.8.0-rc4", "repository": {"type": "git", "url": "https://github.com/threefoldtech/tfgrid-sdk-ts.git"}, "homepage": "https://github.com/threefoldtech/tfgrid-sdk-ts/blob/development/packages/rmb_direct_client/README.md", "author": "am<PERSON><PERSON><PERSON><PERSON><PERSON> <amira<PERSON><PERSON><PERSON><PERSON>@gmail.com>", "license": "MIT", "files": ["/dist"], "publishConfig": {"access": "public"}, "scripts": {"start": "ts-node --project ./tsconfig-node.json ./examples/direct/node.ts", "build": "npm-run-all es6-build node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig-es6.json"}, "dependencies": {"@noble/secp256k1": "^1.7.1", "@polkadot/api": "^8.9.1", "@threefold/tfchain_client": "2.8.0-rc4", "@threefold/types": "2.8.0-rc4", "base64url": "^3.0.1", "bip39": "^3.1.0", "buffer": "^6.0.3", "crypto-js": "^4.1.1", "google-protobuf": "^3.21.2", "js-crypto-aes": "^1.0.4", "uuid": "^8.3.2", "ws": "^8.12.0"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/google-protobuf": "^3.15.6", "@types/node": "^18.11.18", "@types/uuid": "^9.0.0", "@types/ws": "^8.5.4", "nodemon": "^2.0.20", "npm-run-all": "^4.1.5", "protoc-gen-ts": "^0.8.5", "ts-node": "^10.9.1", "typescript": "^4.9"}, "main": "./dist/node/index.js", "module": "./dist/es6/index.js", "types": "dist/es6/index.d.ts", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}}