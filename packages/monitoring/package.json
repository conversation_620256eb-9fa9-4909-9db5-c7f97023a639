{"name": "@threefold/monitoring", "version": "2.8.0-rc4", "description": "Threefold monitoring package", "license": "Apache-2.0", "main": "./dist/node/index.js", "module": "./dist/es6/index.js", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}, "types": "dist/es6/index.d.ts", "files": ["/dist"], "scripts": {"example": "yarn run ts-node --project tsconfig-node.json", "build": "npm-run-all es6-build node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig-es6.json", "test": "jest "}, "author": "<PERSON>", "private": false, "publishConfig": {"access": "public"}, "dependencies": {"@threefold/rmb_direct_client": "2.8.0-rc4", "@threefold/tfchain_client": "2.8.0-rc4", "@threefold/types": "2.8.0-rc4", "chalk": "4.1.2", "ts-node": "^10.9.1", "typescript": "^5.3.3", "url-join": "^4.0.1"}, "devDependencies": {"@types/jest": "29.5.11", "jest": "29.7.0", "ts-jest": "29.1.2"}}