import { resolveServiceStatus, sendRequest } from "../helpers/utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ServiceStatus } from "../types";
import { ServiceBase } from "./serviceBase";

export class GridProxyMonitor extends ServiceBase implements ILivenessChecker {
  constructor(ServiceUrl?: string) {
    super("GridProxy");
    if (ServiceUrl) this.url = ServiceUrl;
  }
  async isAlive(url = this.url): Promise<ServiceStatus> {
    if (!url) throw new Error("Can't access before initialization");
    return resolveServiceStatus(sendRequest(url, { method: "Get" }));
  }
}
