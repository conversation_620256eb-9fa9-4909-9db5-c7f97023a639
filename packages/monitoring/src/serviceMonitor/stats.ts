import { resolveServiceStatus, sendRequest } from "../helpers/utils";
import { IL<PERSON><PERSON><PERSON><PERSON><PERSON>, ServiceStatus } from "../types";
import { ServiceBase } from "./serviceBase";
export class StatsMonitor extends ServiceBase implements ILivenessChecker {
  constructor(ServiceUrl?: string) {
    super("Stats");
    if (ServiceUrl) this.url = ServiceUrl;
  }
  async isAlive(url = this.url): Promise<ServiceStatus> {
    if (!url) throw new Error("Can't access before initialization");
    return resolveServiceStatus(sendRequest(`${url}/api/stats-summary`, { method: "Get" }));
  }
}
