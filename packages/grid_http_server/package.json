{"name": "@threefold/grid_http_server", "author": "<PERSON>", "version": "2.8.0-rc4", "license": "ISC", "homepage": "https://github.com/threefoldtech/tfgrid-sdk-ts/blob/development/packages/grid_http_server/README.md", "repository": {"type": "git", "url": "https://github.com/threefoldtech/tfgrid-sdk-ts.git"}, "publishConfig": {"access": "public"}, "dependencies": {"@threefold/grid_client": "2.8.0-rc4", "express": "^4.21.2", "http-server": "^14.1.1", "typescript": "^4.7.4"}, "devDependencies": {"ts-node": "^10.9.1"}, "main": "./dist/index.js", "types": "dist/index.d.ts", "files": ["/dist"], "bin": {"grid_http_server": "./dist/server.js"}, "private": false, "scripts": {"build": "tsc --build tsconfig.json", "start": "ts-node --project tsconfig.json src/index.ts"}}