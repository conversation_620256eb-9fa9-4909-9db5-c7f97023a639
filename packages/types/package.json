{"name": "@threefold/types", "version": "2.8.0-rc4", "main": "dist/node/index.js", "types": "dist/es6/index.d.ts", "scripts": {"build": "npm-run-all es6-build node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig-es6.json"}, "module": "./dist/es6/index.js", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}, "files": ["/dist"], "license": "MIT", "devDependencies": {"@types/node": "^18.11.9", "ts-node": "^10.9.1", "typescript": "^4.9.3"}, "private": false, "publishConfig": {"access": "public"}}