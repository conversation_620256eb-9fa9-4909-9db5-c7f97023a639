{"name": "@threefold/stats", "version": "2.8.0-rc4", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "dependencies": {"@threefold/gridproxy_client": "2.8.0-rc4", "vue": "^3.3.5", "vuetify": "^3.3.21"}, "devDependencies": {"@tsconfig/node18": "^18.2.2", "@types/jsdom": "^21.1.3", "@types/node": "^18.17.17", "@vitejs/plugin-vue": "^4.3.4", "@vitejs/plugin-vue-jsx": "^3.0.2", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "jsdom": "^22.1.0", "npm-run-all2": "^6.0.6", "start-server-and-test": "^2.0.0", "typescript": "~5.2.0", "vite": "^4.4.9", "vitest": "^0.34.4", "vue-tsc": "^1.8.11"}}