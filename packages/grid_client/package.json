{"name": "@threefold/grid_client", "author": "<PERSON>", "version": "2.8.0-rc4", "license": "ISC", "homepage": "https://github.com/threefoldtech/tfgrid-sdk-ts/tree/development/packages/grid_client/README.md", "repository": {"type": "git", "url": "https://github.com/threefoldtech/tfgrid-sdk-ts.git", "directory": "packages/grid_client"}, "publishConfig": {"access": "public"}, "dependencies": {"@jimber/pkid": "1.0.4", "@noble/secp256k1": "^1.7.1", "@threefold/gridproxy_client": "2.8.0-rc4", "@threefold/monitoring": "2.8.0-rc4", "@threefold/rmb_direct_client": "2.8.0-rc4", "@threefold/tfchain_client": "2.8.0-rc4", "@threefold/types": "2.8.0-rc4", "algosdk": "^1.19.0", "appdata-path": "^1.0.0", "await-lock": "^2.2.2", "axios": "^1.7.8", "bip39": "^3.0.4", "buffer": "^6.0.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "crypto-js": "^4.1.1", "decimal.js": "^10.3.1", "libsodium-wrappers": "^0.7.10", "netaddr": "^1.1.0", "private-ip": "^2.3.3", "reflect-metadata": "^0.1.13", "semver": "^7.6.2", "stellar-sdk": "^10.4.1", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "typescript": "^4.7.4", "url-join": "^4.0.1", "url-parse": "^1.5.10", "uuid4": "^2.0.2"}, "devDependencies": {"@types/jest": "^29.2.6", "@types/semver": "^7.5.8", "jest": "^29.3.1", "jest-junit": "^14.0.1", "node-ssh": "^13.0.0", "npm-run-all": "^4.1.5", "random-ipv6": "^1.0.2", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "tsconfig-paths-webpack-plugin": "^4.0.0", "typedoc": "^0.22.10"}, "main": "./dist/node/index.js", "module": "./dist/es6/index.js", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}, "types": "dist/es6/index.d.ts", "files": ["/dist"], "private": false, "scripts": {"build": "npm-run-all es6-build node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig-es6.json", "rmb_server": "ts-node --project tsconfig-node.json src/server/rmb_server.ts", "generate-docs": "typedoc --tsconfig tsconfig-es6.json src/index.ts --out docs/api", "serve-docs": "http-server docs/api", "test": "jest"}}