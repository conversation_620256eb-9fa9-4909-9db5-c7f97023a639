<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="649px" height="321px" viewBox="-0.5 -0.5 649 321" content="&lt;mxfile host=&quot;app.diagrams.net&quot; modified=&quot;2022-08-20T19:37:25.060Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/104.0.0.0 Safari/537.36&quot; etag=&quot;ZceuyWtBVF4YUGt_RyQj&quot; version=&quot;20.2.5&quot; type=&quot;google&quot;&gt;&lt;diagram id=&quot;-0ppZ22h-bgFTyFkS23m&quot; name=&quot;Page-1&quot;&gt;7Vhtb9s2EP4t+2Cg/WBDr479MXHSZsNWFAiGtvsy0BItcaZIjaItu7++R5HUCxW3btDZGzAEiMi745H33MM7JpNwVRzeClTmv/EU00ngpYdJeD8JgsV8Ab+V4KgF0SLQgkyQVIv8TvBEPmMj9Ix0R1JcDQwl51SScihMOGM4kQMZEoLXQ7MNp8NdS5ThkeApQXQs/UBSmZuwYq+TP2KS5XZn3zOaAlljI6hylPK6JwofJuFKcC71qDisMFXYWVz0ujcntO3BBGbynAU/P3722Z/LX/5Yvi22x78+vKnfxdNQe9kjujMBT4I5BX93VYmYOrU8Gijmf+/UUe8STrmYhLegFNn6FWy6gqH9vAaLBmcmpxtUEHrUpo+Y7rEkCerpqybbSusH5aGv0JsqDeOiQLSn2yNBEHwpyZDcCcWNr9olqDxlUpvEKWUEETQaiqXEYgrhJ4Rl45VclDlixmWgZRIf5BTBiZgWJ5ASLHo6wtImS0rp2VAbjRTgbAP+7U4Ma21N0oa8vW1qLtLhwVpfEMt6S8Cd8llJwbd4Wmu+DuzWKNlmgu9YOnXyCDdTpzCIlmYQ22w2XlOccIEk4Wwqc5JsGa7M8Qgjklh8XNteLr9q1zvOwG5DOZIuOCmpSoqO1pwSUATeT6QouZBIIX3XERZGmfq+g9rUgGkYDldFk1yrzS1t+R40MGF1e3xQ1zmR+AnAV9oaah3IcllQozb3CAuI6+QF9dtrD+US8wJLcQQTu8BWiqMzr7vC08ryXtFZGBkytS5rXXflAAamInxHdYhG1cFgGFkM16KDb44KBQpbV+rz6pGkQHowVmteXx3daDFEN5xfG934FLr+GejeJom6f/9WdK/P3fkpdMOrg+Ve9OtT8WYE1u8VNDAXqGqLZaIOqHplyYnqcg97iLoy4LRvHGWQoipvUPWGCOr2tNL1/t4U9Q2h1IomQRh76gfkFK0xfc8ropoE6NrWqjIAzwn6q2Ow5lLyomdwq/vyveQqk3wnVbdYtc9FtYlp3Z1ziKNUMReHTL1qZ3yzIQmepXgPn2pGUam9/QAy3PhDMgTxmAzxYsyFMPqHuLB45kno8ACz9Fa9rRVkFFUVSZq0IiHH4l7iu3umQMcHIj8ajRp/6snvD/3J0U4YxPexP/lk16tJt6iZ2VX69DgdvfOdDEGEfCcS/O2aAoFmWH6rso8z3s/oM7fbygSm8CTaD4/7XJrNDu/VReyVYs8hlFs1dJhmVf8PBtdR7DgKHUcah5GjhnVt2C8n4vLCRPRm8dlU/IGkiq5JljhyWlH8QrK4jgLvsmSxVfQ/zZZrscC/Wc5i50kSvIwH/uLKPBi/+y7YvlpOfE8DM6su08LC/1vYJdl46R7WZ6N/qQ4Wn8mpm2tyaj53Op1LhXM5NXqwu+R8Madg2v07WJt3/1MPH74A&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><rect x="0" y="0" width="120" height="80" rx="12" ry="12" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 40px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(248, 249, 250); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">Node 2</span></div></div></div></foreignObject><text x="60" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Node 2</text></switch></g><rect x="360" y="240" width="120" height="80" rx="12" ry="12" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 280px; margin-left: 361px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Node 4<br /> (Hidden Node)</div></div></div></foreignObject><text x="420" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Node 4...</text></switch></g><rect x="360" y="0" width="120" height="80" rx="12" ry="12" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 40px; margin-left: 361px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Node 1<br /> (Access Node)</div></div></div></foreignObject><text x="420" y="44" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Node 1...</text></switch></g><rect x="0" y="240" width="120" height="80" rx="12" ry="12" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 280px; margin-left: 1px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">Node 3</div></div></div></foreignObject><text x="60" y="284" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">Node 3</text></switch></g><rect x="590" y="130" width="58" height="34" fill="none" stroke="none" pointer-events="all"/><path d="M 623.53 161.86 C 624.09 161.86 624.48 161.64 624.37 161.48 L 623.26 159.86 C 623.12 159.61 622.92 159.57 622.59 159.57 L 615.41 159.57 C 615.06 159.57 614.88 159.62 614.71 159.87 L 613.68 161.47 C 613.48 161.72 614.08 161.86 614.51 161.86 Z M 640.32 156.61 L 640.32 132.96 L 597.65 132.96 L 597.65 156.61 Z M 593 164 C 591.87 163.99 590.85 163.62 590.42 163.03 C 590 162.46 590.17 161.89 590.53 161.5 L 594.66 157.35 L 594.66 132.83 C 594.66 131.6 595.78 130 597.63 130 L 640.33 130 C 641.77 130 643.31 131.14 643.31 132.99 L 643.31 157.35 L 647.48 161.54 C 647.83 161.93 648 162.45 647.58 163.01 C 647.03 163.72 645.99 163.95 645.02 164 Z" fill="#505050" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 171px; margin-left: 619px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">User</div></div></div></foreignObject><text x="619" y="183" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">User</text></switch></g><path d="M 125.3 236.47 L 354.7 83.53" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 120.93 239.38 L 124.81 232.58 L 125.3 236.47 L 128.7 238.41 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 359.07 80.62 L 355.19 87.42 L 354.7 83.53 L 351.3 81.59 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 420 233.63 L 420 86.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 420 238.88 L 416.5 231.88 L 420 233.63 L 423.5 231.88 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 420 81.12 L 423.5 88.12 L 420 86.37 L 416.5 88.12 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 59.52 235.63 L 59.98 86.37" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 59.5 240.88 L 56.03 233.87 L 59.52 235.63 L 63.03 233.89 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 60 81.12 L 63.47 88.13 L 59.98 86.37 L 56.47 88.11 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 126.37 40 L 353.63 40" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 121.12 40 L 128.12 36.5 L 126.37 40 L 128.12 43.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 358.88 40 L 351.88 43.5 L 353.63 40 L 351.88 36.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 485.74 82.77 L 584.26 130.26" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 481.01 80.49 L 488.83 80.37 L 485.74 82.77 L 485.79 86.68 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 588.99 132.54 L 581.17 132.65 L 584.26 130.26 L 584.21 126.34 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>