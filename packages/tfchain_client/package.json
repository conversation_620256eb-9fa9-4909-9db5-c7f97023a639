{"name": "@threefold/tfchain_client", "version": "2.8.0-rc4", "description": "A client for TF chain", "private": false, "publishConfig": {"access": "public"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "npm-run-all es6-build node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig-es6.json", "generate-docs": "typedoc --tsconfig tsconfig-es6.json src/index.ts --out docs/api", "serve-docs": "http-server docs/api", "generate-types": "bash ./scripts/generateTypes.bash"}, "repository": {"type": "git", "url": "https://github.com/threefoldtech/tfgrid-sdk-ts.git"}, "keywords": ["tfchain"], "author": "<PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/threefoldtech/tfgrid-sdk-ts/issues"}, "homepage": "https://github.com/threefoldtech/tfgrid-sdk-ts/tree/development/packages/tfchain_client/README.md", "main": "./dist/node/index.js", "module": "./dist/es6/index.js", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}, "types": "dist/es6/index.d.ts", "files": ["/dist"], "devDependencies": {"@polkadot/typegen": "^8.9.1", "http-server": "^14.1.1", "npm-run-all": "^4.1.5", "ts-node": "^10.9.1", "typedoc": "^0.23.28", "typescript": "^5.0.2"}, "dependencies": {"@polkadot/api": "^8.9.1", "@threefold/types": "2.8.0-rc4", "await-lock": "^2.2.2", "bip39": "^3.1.0", "moment": "^2.30.1"}}