{
    "compilerOptions": {
        "module": "commonjs",
        "target": "esnext",
        "lib": [
            "ESNext",
            "DOM"
        ],
        "types": [
            "node",
        ],
        "declaration": true,
        "declarationMap": true,
        "outDir": "./dist/node",
        "esModuleInterop": true,
        "resolveJsonModule": true,
        "emitDecoratorMetadata": true,
        "experimentalDecorators": true,
        "allowJs": true,
        "baseUrl": ".",
        "skipLibCheck": true, /* Skip type checking all .d.ts files. */
        "paths": {
            "@polkadot/types/lookup": ["src/interfaces/chain/types-lookup.ts"],
            "@polkadot/api-augment*": ["src/interfaces/chain/augment-api.ts"]
        }

    },
    "include": [
        "src/**/*"
    ]
}
