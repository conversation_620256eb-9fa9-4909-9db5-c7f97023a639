{"compilerOptions": {"target": "ES2017", "module": "ES2015", "moduleResolution": "node", "types": ["node"], "baseUrl": "./src", "outDir": "./dist/es6", "declaration": true, "declarationMap": true, "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "noImplicitAny": false, "resolveJsonModule": true}, "include": ["src"], "exclude": ["node_modules", "tests"]}