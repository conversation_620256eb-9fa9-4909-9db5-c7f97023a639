# graphql_client

This a Typescript client to send requests to graghql based on the network.
Allow to choose fields and turn chosen fields into type on the fly.

## Installation

```bash
yarn install
```

## Building

```bash
yarn build
```

### Supported URLs

- [devnet](https://graphql.dev.grid.tf/graphql)
- [qanet](https://graphql.qa.grid.tf/graphql)
- [testnet](https://graphql.test.grid.tf/graphql)
- [mainnet](https://graphql.grid.tf/graphql)
