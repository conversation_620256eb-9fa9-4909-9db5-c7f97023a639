{"compilerOptions": {"module": "CommonJS", "target": "ESNext", "lib": ["ESNext", "DOM"], "types": ["node", "jest"], "declaration": true, "declarationMap": true, "outDir": "./dist/node", "esModuleInterop": true, "resolveJsonModule": true, "allowJs": true, "baseUrl": "./src", "skipLibCheck": true, "sourceMap": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": false}, "include": ["src"], "exclude": ["node_modules", "tests"]}