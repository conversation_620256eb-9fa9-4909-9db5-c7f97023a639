{"name": "@threefold/graphql_client", "version": "2.8.0-rc4", "main": "./dist/node/index.js", "module": "./dist/es6/index.js", "exports": {"require": "./dist/node/index.js", "import": "./dist/es6/index.js"}, "types": "dist/es6/index.d.ts", "files": ["/dist"], "license": "MIT", "scripts": {"build": "npm run es6-build && npm run node-build", "node-build": "tsc --build tsconfig-node.json", "es6-build": "tsc --build tsconfig.json"}, "dependencies": {"@threefold/types": "2.8.0-rc4", "ts-mixer": "^6.0.2"}, "devDependencies": {"typescript": "^4.9.3"}, "private": false, "publishConfig": {"access": "public"}}